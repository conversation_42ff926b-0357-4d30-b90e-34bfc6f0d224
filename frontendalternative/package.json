{"name": "frontendalternative", "version": "0.1.0", "private": true, "dependencies": {"@emotion/react": "^11.14.0", "@emotion/styled": "^11.14.0", "@mui/icons-material": "^7.1.1", "@mui/lab": "^7.0.0-beta.13", "@mui/material": "^7.1.1", "@testing-library/dom": "^10.4.0", "@testing-library/jest-dom": "^6.6.3", "@testing-library/react": "^16.3.0", "@testing-library/user-event": "^13.5.0", "@types/jest": "^27.5.2", "@types/lodash": "^4.17.17", "@types/node": "^16.18.126", "@types/react": "^19.1.6", "@types/react-dom": "^19.1.6", "axios": "^1.9.0", "copy-to-clipboard": "^3.3.3", "lodash": "^4.17.21", "react": "^19.1.0", "react-dom": "^19.1.0", "react-markdown": "^10.1.0", "react-scripts": "5.0.1", "react-select": "^5.10.1", "react-syntax-highlighter": "^15.6.1", "typescript": "^4.9.5", "web-vitals": "^2.1.4"}, "scripts": {"start": "react-scripts start", "build": "react-scripts build", "test": "react-scripts test", "eject": "react-scripts eject"}, "eslintConfig": {"extends": ["react-app", "react-app/jest"]}, "browserslist": {"production": [">0.2%", "not dead", "not op_mini all"], "development": ["last 1 chrome version", "last 1 firefox version", "last 1 safari version"]}, "devDependencies": {"@types/react-syntax-highlighter": "^15.5.13"}}